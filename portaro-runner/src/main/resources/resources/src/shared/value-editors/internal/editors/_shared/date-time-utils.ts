import {DateTime} from 'luxon';
import type {Formatter, Parser} from '../../forms/types';
import {LocalizationAwareError} from '../../errors/localization-aware-error';

const ISO_FORMAT_STRING = 'ISO';

const DEFAULT_DATE_FORMATS = [
    'd.L.y',
    'dd.L.y',
    'd.LL.y',
    'dd.LL.y'
];

export function dateParser({customModelFormat, customViewFormat}: {customModelFormat?: string, customViewFormat?: string} = {}): Parser {
    return (value: string) => {
        const parsedDate = parseDate(value, customModelFormat, customViewFormat);
        if (!!value && !parsedDate) {
            throw new LocalizationAwareError('date', 'Parsed value is not date');
        }
        return parsedDate;
    }
}

export function dateFormatter({customModelFormat, customViewFormat}: {customModelFormat?: string, customViewFormat?: string} = {}): Formatter {
    return (value: string) => formatDate(value, customModelFormat, customViewFormat);
}

/**
 * @name parseDate
 *
 * @param {string} parsingDate parsed date in default or custom view format (if `customViewFormat` is provided)
 * @param {string=} customModelFormat Custom model format
 * @param {string=} customViewFormat Custom view format
 *
 * @return {string | null} parsed date in ISO format or custom format (if `customModelFormat` is provided) if date is valid, otherwise null
 *
 * @description
 * Parse string date from predefined format or custom view format (if provided) to ISO format or custom model format.
 * If given string is not valid date it returns null
 */
export function parseDate(parsingDate: string, customModelFormat?: string, customViewFormat?: string): string | null {
    if (!parsingDate) {
        return null;
    }

    try {
        let result: DateTime | {isValid: boolean} = {isValid: false};
        const parsingPipeline = [...DEFAULT_DATE_FORMATS];

        if (customViewFormat) {
            parsingPipeline.unshift(customViewFormat);
        }

        const formatsIterator = parsingPipeline[Symbol.iterator]();
        let format = formatsIterator.next();

        while (!result.isValid && !format.done) {
            if (format.value.toUpperCase() === ISO_FORMAT_STRING) {
                result = DateTime.fromISO(parsingDate);
            } else {
                result = DateTime.fromFormat(parsingDate, format.value);
            }
            format = formatsIterator.next();
        }

        if (result.isValid) {
            const dateTime = result as DateTime;

            if (dateTime.year > 9999) { // date should be invalid if year has more than 4 digits (ISO with more than 4 digit year is not standard and could cause problems)
                return null;
            }

            if (customModelFormat) {
                return dateTime.toFormat(customModelFormat);
            }

            return dateTime.toISO();
        }
    } catch (e) {
        throw new Error('Date parsing errors', {cause: e});
    }

    return null;
}

/**
 * @name formatDate
 *
 * @param {string} formattingDate date to be formatted in ISO or custom model format
 * @param {string=} customModelFormat Custom model format
 * @param {string=} customViewFormat Custom view format
 *
 * @return {string | null} formatted date in ISO format or custom format (if `customViewFormat` is provided) if date is valid, otherwise null
 *
 * @description
 * Format date in ISO or custom model format to ISO or custom view format (if provided).
 * If given string is not valid date it returns null
 */
export function formatDate(formattingDate: string, customModelFormat?: string, customViewFormat?: string): string | null {
    if (!formattingDate) {
        return null;
    }

    try {
        let parsedDate: DateTime;

        if (customModelFormat) {
            parsedDate = DateTime.fromFormat(formattingDate, customModelFormat);
        } else {
            parsedDate = DateTime.fromISO(formattingDate);
        }

        if (customViewFormat && customViewFormat.toUpperCase() !== ISO_FORMAT_STRING) {
            return parsedDate.toFormat(customViewFormat);
        }

        return parsedDate.toISO();
    } catch (e) {
        throw new Error('Date formatting error', {cause: e});
    }
}


/**
 * @name validateMinDate
 *
 * @param {string} value tested date in ISO format or in custom format defined in `customModelFormat` parameter
 * @param {string} minDateInISO Date in ISO format
 * @param {string=} customModelFormat Custom model format
 *
 * @returns {boolean}
 *
 * @description
 * Function returns validation on min date.
 */
export function validateMinDate(value: string, minDateInISO: string, customModelFormat?: string): boolean {
    return dateRestriction(value, minDateInISO, customModelFormat ?? null, (currentDate, restrictingDate) => currentDate >= restrictingDate);
}

/**
 * @name validateMaxDate
 *
 * @param {string} value tested date in ISO format or in custom format defined in `customModelFormat` parameter
 * @param {string} maxDateInISO Date in ISO format
 * @param {string=} customModelFormat Custom model format
 *
 * @returns {boolean}
 *
 * @description
 * Function returns validation on max date.
 *
 */
export function validateMaxDate(value: string, maxDateInISO: string, customModelFormat?: string): boolean {
    return dateRestriction(value, maxDateInISO, customModelFormat ?? null, (currentDate, restrictingDate) => currentDate <= restrictingDate);
}

/**
 * @name dateRestriction
 * This function performs some checks and parsings of input dates and then returns `comparator` result.
 *
 * @param {string} value tested date in ISO format or in custom format defined in `customModelFormat` parameter
 * @param {string} restrictingDateInISO limit date in ISO format
 * @param {string} customModelFormat Custom model format
 * @param {(currentDate: DateTime, restrictingDate: DateTime) => boolean} comparator Executive logic - actual date comparison
 *
 * @returns {boolean}
 * @private
 */
function dateRestriction(value: string, restrictingDateInISO: string, customModelFormat: string | null, comparator: (currentDate: DateTime, restrictingDate: DateTime) => boolean): boolean {
    if (!restrictingDateInISO) {
        return true;
    }

    if (!value) {
        return true;
    }

    let currentDateInISO: string;
    if (!!customModelFormat) {
        currentDateInISO = DateTime.fromFormat(value, customModelFormat).toISO() ?? DateTime.fromISO(value).toISO();
    } else {
        currentDateInISO = DateTime.fromISO(value).toISO();
    }

    if (currentDateInISO === null) {
        return true;
    }

    const currentDate = DateTime.fromISO(currentDateInISO);
    const restrictingDate = DateTime.fromISO(restrictingDateInISO);

    if (!restrictingDate.isValid) {
        throw new Error(`Date validation error: restrictingDate argument for date validation is not in ISO format: ${restrictingDateInISO}`);
    }

    return comparator(currentDate, restrictingDate);
}