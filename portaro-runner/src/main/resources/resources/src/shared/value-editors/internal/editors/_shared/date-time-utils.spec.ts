import {dateParser, formatDate, parseDate, validateMaxDate, validateMinDate} from './date-time-utils';
import {DateTime} from 'luxon';
import {LocalizationAwareError} from '../../errors/localization-aware-error';

describe(' value editor datetime utils', () => {

   describe('parseDate', () => {
       it('should parse day.month.year to ISO', () => {
           expect(parseDate('10.4.2005')).toEqual(DateTime.fromFormat('10.4.2005', 'd.L.y').toISO());
       });

       it('should parse custom format to ISO', () => {
           expect(parseDate('2005_04_10', null, 'y_LL_dd')).toEqual(DateTime.fromFormat('10.4.2005', 'd.L.y').toISO());
       });

       it('should parse day.month.year to dd-mm-yyyy', () => {
           expect(parseDate('10.4.2005', 'dd-LL-y')).toEqual('10-04-2005');
       });

       it('should parse year to day.month.year date', () => {
           expect(parseDate('1996', 'd.L.y', 'y')).toEqual('1.1.1996');
       });

       it('should parse pre-1891 date to ISO with correct historic timezone +00:57', () => {
           expect(parseDate('1.1.1666')).toEqual(DateTime.fromFormat('1.1.1666', 'd.L.y').toISO());
       });

       it('should return null for invalid dates', () => {
           expect(parseDate(null)).toEqual(null);
           expect(parseDate('')).toEqual(null);
           expect(parseDate('sadasdada')).toEqual(null);
           expect(parseDate('10x4x2005')).toEqual(null);
       });
   });

   describe('dateParser', () => {
       it('should throw LocalizationAwareError if parsing was unsuccessful', () => {
           const parser = dateParser();
           expect(() => parser('dsasda')).toThrowMatching((error) => {
               return error instanceof LocalizationAwareError && error.localizationKey === 'date';
           })
       });
   });

   describe('formatDate', () => {
       it('should format ISO to day.month.year', () => {
           expect(formatDate(DateTime.fromFormat('10.4.2005', 'd.L.y').toISO(), null, 'd.L.y')).toEqual('10.4.2005');
       });

       it('should format ISO to custom format', () => {
           expect(formatDate(DateTime.fromFormat('10.4.2005', 'd.L.y').toISO(), null, 'y_LL_dd')).toEqual('2005_04_10');
       });

       it('should format custom dd-mm-yyyy format to day.month.year', () => {
           expect(formatDate('10-04-2005', 'dd-LL-y', 'd.L.y')).toEqual('10.4.2005');
       });

       it('should format day.month.year date to year', () => {
           expect(formatDate('1.1.1996', 'd.L.y', 'y')).toEqual('1996');
       });

       it('should format pre-1891 ISO date to correct date', () => {
           expect(formatDate(DateTime.fromFormat('1.1.1666', 'd.L.y').toISO(), null,'d.L.y')).toEqual('1.1.1666');
       });

       it('should return null for invalid dates', () => {
           expect(formatDate(null)).toEqual(null);
           expect(formatDate('')).toEqual(null);
           expect(formatDate('sadasdada')).toEqual(null);
           expect(formatDate('10x4x2005')).toEqual(null);
       });
   });

   describe('validateMinDate', () => {

       describe('value in ISO format', () => {
           const restrictionDate = DateTime.fromFormat('10.3.1990', 'd.L.y').toISO();
           it('should be valid for later date', () => {
               const testedDate = DateTime.fromFormat('11.3.1990', 'd.L.y').toISO();
               expect(validateMinDate(testedDate, restrictionDate)).toEqual(true);
           });

           it('should be valid for the limit date', () => {
               const testedDate = DateTime.fromFormat('10.3.1990', 'd.L.y').toISO();
               expect(validateMinDate(testedDate, restrictionDate)).toEqual(true);
           });

           it('should be invalid for earlier date', () => {
               const testedDate = DateTime.fromFormat('9.3.1990', 'd.L.y').toISO();
               expect(validateMinDate(testedDate, restrictionDate)).toEqual(false);
           });
       });

       describe('value in custom format', () => {
           const restrictionDate = DateTime.fromFormat('10.3.1990', 'd.L.y').toISO();
           it('should be valid for later date', () => {
               const testedDate = '1990-03-11';
               expect(validateMinDate(testedDate, restrictionDate, 'y-LL-dd')).toEqual(true);
           });

           it('should be valid for the limit date', () => {
               const testedDate ='1990-03-10';
               expect(validateMinDate(testedDate, restrictionDate, 'y-LL-dd')).toEqual(true);
           });

           it('should be invalid for earlier date', () => {
               const testedDate = '1990-03-09';
               expect(validateMinDate(testedDate, restrictionDate, 'y-LL-dd')).toEqual(false);
           });
       });
   });

   describe('validateMaxDate', () => {
       describe('value in ISO format', () => {
           const restrictionDate = DateTime.fromFormat('10.3.1990', 'd.L.y').toISO();
           it('should be valid for earlier date', () => {
               const testedDate = DateTime.fromFormat('9.3.1990', 'd.L.y').toISO();
               expect(validateMaxDate(testedDate, restrictionDate)).toEqual(true);
           });

           it('should be valid for the limit date', () => {
               const testedDate = DateTime.fromFormat('10.3.1990', 'd.L.y').toISO();
               expect(validateMaxDate(testedDate, restrictionDate)).toEqual(true);
           });

           it('should be invalid for later date', () => {
               const testedDate = DateTime.fromFormat('11.3.1990', 'd.L.y').toISO();
               expect(validateMaxDate(testedDate, restrictionDate)).toEqual(false);
           });
       });

       describe('value in custom format', () => {
           const restrictionDate = DateTime.fromFormat('10.3.1990', 'd.L.y').toISO();
           it('should be valid for earlier date', () => {
               const testedDate = '1990-03-09';
               expect(validateMaxDate(testedDate, restrictionDate, 'y-LL-dd')).toEqual(true);
           });

           it('should be valid for the limit date', () => {
               const testedDate ='1990-03-10';
               expect(validateMaxDate(testedDate, restrictionDate, 'y-LL-dd')).toEqual(true);
           });

           it('should be invalid for later date', () => {
               const testedDate = '1990-03-11';
               expect(validateMaxDate(testedDate, restrictionDate, 'y-LL-dd')).toEqual(false);
           });
       });
   });
});