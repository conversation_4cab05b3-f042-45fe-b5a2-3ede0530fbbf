import type {DateTime} from 'luxon';

type InclusiveStartBracket = '[';
type ExclusiveStartBracket = '(';
type InclusiveEndBracket = ']';
type ExclusiveEndBracket = ')';

type IntervalStartBracket = InclusiveStartBracket | ExclusiveStartBracket;
type IntervalEndBracket = InclusiveEndBracket | ExclusiveEndBracket;

type DateTimeWithZoneString = string & {readonly __brand: 'DateTimeWithZoneString'};
type IntervalStartDate = DateTimeWithZoneString | '-infinity';
type IntervalEndDate = DateTimeWithZoneString | 'infinity';

type EmptyIntervalString = 'empty';

export type IntervalString =
    EmptyIntervalString
    | `${IntervalStartBracket}${IntervalStartDate},${IntervalEndDate}${IntervalEndBracket}`;

interface IntervalObject {
    from?: DateTime; // Null = -infinity
    to?: DateTime; // Null = infinity
}

// TODO: This function should take an IntervalString
export function parseIntervalStringToObject(interval: IntervalString): IntervalObject {
    return {};
}

// Both from and date should return as inclusive
export function parseObjectToIntervalString(interval: IntervalObject): IntervalString {
    return 'empty';
}